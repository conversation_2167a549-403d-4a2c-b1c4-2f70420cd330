{"name": "basic-example", "productName": "Electron Router DOM Example", "description": "An Electron Router DOM basic example", "version": "0.0.0", "private": true, "main": "./out/main/index.js", "homepage": "https://daltonmenezes.github.io/electron-router-dom", "license": "MIT", "author": {"name": "<PERSON><PERSON>"}, "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev --watch", "build": "electron-vite build", "predist": "pnpm build", "dist": "electron-builder", "electron:build:win": "electron-vite build && electron-builder --win", "electron:build:mac": "electron-vite build && electron-builder --mac"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^2.2.0", "electron": "^36.5.0", "electron-builder": "^23.6.0", "electron-vite": "^3.1.0", "eslint": "8.57.0", "postcss": "^8.4.41", "rollup-plugin-inject-process-env": "^1.3.1", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "vite": "^6.0.0", "vite-tsconfig-paths": "^3.5.2"}, "dependencies": {"@electron-toolkit/preload": "^1.0.2", "@electron-toolkit/utils": "^1.0.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@supabase/supabase-js": "^2.50.1", "@types/puppeteer": "^5.4.7", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "electron-router-dom": "v2.1.0", "framer-motion": "^12.19.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "puppeteer": "^24.11.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.4.0"}, "eslintIgnore": ["dist", "out"]}