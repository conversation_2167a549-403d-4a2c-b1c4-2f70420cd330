import React from 'react';
import { InvoiceData } from '../lib/transactionService';

interface InvoiceTemplateProps {
  invoiceData: InvoiceData;
  onClose: () => void;
  onGeneratePDF: () => void;
}

export function InvoiceTemplate({ invoiceData, onClose, onGeneratePDF }: InvoiceTemplateProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const invoiceContent = (
    <div className="invoice-container bg-white p-8 !w-full mx-auto">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">INVOICE</h1>
          <p className="text-lg text-gray-600">#{invoiceData.invoiceNumber}</p>
        </div>
        <div className="text-right">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {invoiceData.companyInfo.name}
          </h2>
          <div className="text-gray-600 text-sm">
            <p>{invoiceData.companyInfo.address}</p>
            <p>{invoiceData.companyInfo.phone}</p>
            <p>{invoiceData.companyInfo.email}</p>
          </div>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Bill To:</h3>
          {invoiceData.clientInfo ? (
            <div className="text-gray-700">
              <p className="font-medium">{invoiceData.clientInfo.name}</p>
              <p>{invoiceData.clientInfo.address}</p>
              {invoiceData.clientInfo.phone && <p>{invoiceData.clientInfo.phone}</p>}
              {invoiceData.clientInfo.email && <p>{invoiceData.clientInfo.email}</p>}
            </div>
          ) : (
            <p className="text-gray-500 italic">No client information provided</p>
          )}
        </div>
        <div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">Issue Date:</span>
              <span className="text-gray-900">{formatDate(invoiceData.issueDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">Due Date:</span>
              <span className="text-gray-900">{formatDate(invoiceData.dueDate)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Transaction Items */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Items & Services</h3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  Date
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  Description
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  Category
                </th>
                <th className="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900">
                  Type
                </th>
                <th className="border border-gray-300 px-4 py-3 text-right font-semibold text-gray-900">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody>
              {invoiceData.transactions.map((transaction, index) => (
                <tr key={transaction.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                    {formatDate(transaction.date)}
                  </td>
                  <td className="border border-gray-300 px-4 py-3">
                    <div className="text-sm font-medium text-gray-900">{transaction.title}</div>
                    {transaction.description && (
                      <div className="text-xs text-gray-500 mt-1">{transaction.description}</div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                    {transaction.category || 'Uncategorized'}
                  </td>
                  <td className="border border-gray-300 px-4 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      transaction.type === 'income' 
                        ? 'bg-green-100 text-green-800' 
                        : transaction.type === 'expense'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {transaction.type}
                    </span>
                  </td>
                  <td className="border border-gray-300 px-4 py-3 text-right text-sm font-medium">
                    <span className={
                      transaction.type === 'expense' ? 'text-red-600' : 'text-green-600'
                    }>
                      {transaction.type === 'expense' ? '-' : '+'}
                      {formatCurrency(Math.abs(transaction.amount))}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="space-y-2">
            <div className="flex justify-between py-2 border-b border-gray-200">
              <span className="font-medium text-gray-700">Subtotal:</span>
              <span className="text-gray-900">{formatCurrency(invoiceData.summary.subtotal)}</span>
            </div>
            {invoiceData.summary.tax && invoiceData.summary.tax > 0 && (
              <div className="flex justify-between py-2 border-b border-gray-200">
                <span className="font-medium text-gray-700">Tax:</span>
                <span className="text-gray-900">{formatCurrency(invoiceData.summary.tax)}</span>
              </div>
            )}
            <div className="flex justify-between py-3 border-t-2 border-gray-300">
              <span className="text-lg font-bold text-gray-900">Total:</span>
              <span className="text-lg font-bold text-gray-900">
                {formatCurrency(invoiceData.summary.total)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Notes */}
      {invoiceData.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes:</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{invoiceData.notes}</p>
        </div>
      )}

      {/* Footer */}
      <div className="border-t border-gray-200 pt-6 text-center text-sm text-gray-500">
        <p>Thank you for your business!</p>
        <p className="mt-2">
          Generated on {new Date().toLocaleDateString()} by {invoiceData.companyInfo.name}
        </p>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] overflow-y-auto">
        {/* Screen Header - Hidden when printing */}
        <div className="no-print flex justify-between items-center p-6 border-b border-gray-200 bg-gray-50">
          <h2 className="text-xl font-semibold text-gray-900">Invoice Preview</h2>
          <div className="flex gap-2">
            <button
              onClick={onGeneratePDF}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              📄 Generate PDF
            </button>
            <button
              onClick={() => window.print()}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              🖨️ Print
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              ✕ Close
            </button>
          </div>
        </div>

        {/* Invoice Content */}
        <div id="invoice-content">
          {invoiceContent}
        </div>

        {/* Screen Footer - Hidden when printing */}
        <div className="no-print p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end gap-2">
            <button
              onClick={onGeneratePDF}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              📄 Generate PDF
            </button>
            <button
              onClick={() => window.print()}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              🖨️ Print Invoice
            </button>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print { display: none !important; }
          .invoice-container { 
            margin: 0; 
            padding: 0; 
            max-width: none; 
            box-shadow: none;
          }
          @page { 
            margin: 1in; 
            size: A4; 
          }
        }
      `}</style>
    </div>
  );
}
