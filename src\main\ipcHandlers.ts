import axios from 'axios';
import { ipcMain, dialog, app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  address: {
    street: string;
    suite: string;
    city: string;
    zipcode: string;
    geo: {
      lat: string;
      lng: string;
    };
  };
  phone: string;
  website: string;
  company: {
    name: string;
    catchPhrase: string;
    bs: string;
  };
}

interface Post {
  userId: number;
  id: number;
  title: string;
  body: string;
}

interface Comment {
  postId: number;
  id: number;
  name: string;
  email: string;
  body: string;
}

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  // Fetch todos
  ipcMain.handle('fetch-todos', async () => {
    try {
      console.log('fetch-todos handler called');
      const response = await axios.get<Todo[]>('https://jsonplaceholder.typicode.com/todos');
      console.log('Todos API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  });

  // Fetch users
  ipcMain.handle('fetch-users', async () => {
    try {
      console.log('fetch-users handler called');
      const response = await axios.get<User[]>('https://jsonplaceholder.typicode.com/users');
      console.log('Users API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  });

  // Fetch posts
  ipcMain.handle('fetch-posts', async () => {
    try {
      console.log('fetch-posts handler called');
      const response = await axios.get<Post[]>('https://jsonplaceholder.typicode.com/posts');
      console.log('Posts API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching posts:', error);
      throw error;
    }
  });

  // Fetch comments
  ipcMain.handle('fetch-comments', async () => {
    try {
      console.log('fetch-comments handler called');
      const response = await axios.get<Comment[]>('https://jsonplaceholder.typicode.com/comments');
      console.log('Comments API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching comments:', error);
      throw error;
    }
  });

  // Fetch specific user by ID
  ipcMain.handle('fetch-user-by-id', async (event, userId: number) => {
    try {
      console.log('fetch-user-by-id handler called with ID:', userId);
      const response = await axios.get<User>(`https://jsonplaceholder.typicode.com/users/${userId}`);
      console.log('User API response received for ID:', userId);
      return response.data;
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      throw error;
    }
  });

  // Fetch posts by user ID
  ipcMain.handle('fetch-posts-by-user', async (event, userId: number) => {
    try {
      console.log('fetch-posts-by-user handler called with user ID:', userId);
      const response = await axios.get<Post[]>(`https://jsonplaceholder.typicode.com/users/${userId}/posts`);
      console.log('Posts by user API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching posts by user:', error);
      throw error;
    }
  });

  // Legacy handler (keeping for backward compatibility)
  ipcMain.handle('fetch-data', async () => {
    try {
      console.log('fetch-data handler called (legacy)');
      const response = await axios.get<Todo[]>('https://jsonplaceholder.typicode.com/todos');
      console.log('API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  });

  // Generate PDF from HTML content
  ipcMain.handle('generate-pdf', async (event, htmlContent: string, options: any = {}) => {
    try {
      console.log('generate-pdf handler called');

      // For now, we'll use a simple approach without puppeteer
      // This will save the HTML content and let the user print to PDF
      const userDataPath = app.getPath('userData');
      const tempDir = path.join(userDataPath, 'temp');

      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempHtmlPath = path.join(tempDir, `invoice-${Date.now()}.html`);

      // Create a complete HTML document with styles
      const completeHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        @media print {
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
        }
        @page {
            margin: 1in;
            size: A4;
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

      fs.writeFileSync(tempHtmlPath, completeHtml);

      return {
        success: true,
        filePath: tempHtmlPath,
        message: 'HTML file created successfully. You can open it and print to PDF.'
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  });

  // Save PDF file dialog
  ipcMain.handle('save-pdf-dialog', async (event, defaultName: string = 'invoice.pdf') => {
    try {
      const result = await dialog.showSaveDialog({
        title: 'Save Invoice PDF',
        defaultPath: defaultName,
        filters: [
          { name: 'PDF Files', extensions: ['pdf'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      return result;
    } catch (error) {
      console.error('Error showing save dialog:', error);
      return { canceled: true };
    }
  });

  // Open file in default application
  ipcMain.handle('open-file', async (event, filePath: string) => {
    try {
      const { shell } = require('electron');
      await shell.openPath(filePath);
      return { success: true };
    } catch (error) {
      console.error('Error opening file:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  console.log('IPC handlers setup complete');
}